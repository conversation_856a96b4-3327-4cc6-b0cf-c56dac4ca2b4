#include "binary_trees.h"
#include <stdlib.h>

/**
 * tree_height - Measures the height of a binary tree
 * @tree: Pointer to the root node
 * Return: Height of the tree, -1 if tree is NULL
 */
int tree_height(const binary_tree_t *tree)
{
    int left_height, right_height;

    if (!tree)
        return (-1);

    left_height = tree_height(tree->left);
    right_height = tree_height(tree->right);

    return (1 + (left_height > right_height ? left_height : right_height));
}

/**
 * is_bst_helper - Helper function to check if tree is BST
 * @tree: Pointer to the root node
 * @min: Minimum allowed value
 * @max: Maximum allowed value
 * Return: 1 if BST, 0 otherwise
 */
int is_bst_helper(const binary_tree_t *tree, int min, int max)
{
    if (!tree)
        return (1);

    if (tree->n <= min || tree->n >= max)
        return (0);

    return (is_bst_helper(tree->left, min, tree->n) &&
            is_bst_helper(tree->right, tree->n, max));
}

/**
 * binary_tree_is_avl - Checks if a binary tree is a valid AVL Tree
 * @tree: Pointer to the root node of the tree to check
 * Return: 1 if tree is a valid AVL Tree, 0 otherwise
 */
int binary_tree_is_avl(const binary_tree_t *tree)
{
    int left_height, right_height, balance;

    if (!tree)
        return (0);

    /* Check if it's a valid BST */
    if (!is_bst_helper(tree, INT_MIN, INT_MAX))
        return (0);

    /* Check AVL property for current node */
    left_height = tree_height(tree->left);
    right_height = tree_height(tree->right);
    balance = left_height - right_height;

    if (balance < -1 || balance > 1)
        return (0);

    /* Recursively check left and right subtrees */
    if (tree->left && !binary_tree_is_avl(tree->left))
        return (0);
    if (tree->right && !binary_tree_is_avl(tree->right))
        return (0);

    return (1);
}